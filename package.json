{"name": "modporter-ai", "version": "1.0.0", "description": "AI-powered tool for converting Minecraft Java Edition mods to Bedrock Edition add-ons", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/anchapin/ModPorter-AI.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/anchapin/ModPorter-AI/issues"}, "homepage": "https://github.com/anchapin/ModPorter-AI#readme"}